{"name": "leadwave-whatsapp-desktop", "version": "1.0.0", "description": "Modern WhatsApp automation desktop application built with Electron.js and Baileys", "main": "build/electron.js", "homepage": "./", "scripts": {"start": "electron .", "start:prod": "electron . --prod", "dev": "concurrently \"npm run dev:react\" \"wait-on http://localhost:3000 && electron src/main/main.js\"", "dev:react": "react-scripts start", "build": "react-scripts build && npm run copy-electron-files", "copy-electron-files": "node scripts/copy-electron-files.js", "update-logo": "node scripts/update-logo.js", "build:electron": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never", "dist:professional": "node scripts/build-installer.js", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps", "configure-reseller": "node scripts/configure-reseller.js", "build:reseller": "npm run configure-reseller && npm run dist", "reset-config": "node scripts/configure-reseller.js --reset"}, "keywords": ["electron", "whatsapp", "automation", "baileys", "desktop", "react", "tailwind"], "author": "LeadWave Team", "license": "MIT", "dependencies": {"@hapi/boom": "^10.0.1", "@heroicons/react": "^2.0.18", "@itsukichan/baileys": "github:<PERSON><PERSON><PERSON><PERSON>/<PERSON>s", "archiver": "^7.0.1", "axios": "^1.3.4", "better-sqlite3": "^12.1.1", "crypto-js": "^4.2.0", "csv-parser": "^3.0.0", "date-fns": "^2.29.3", "electron-is-dev": "^2.0.0", "express": "^5.1.0", "googleapis": "^150.0.1", "jimp": "^0.22.12", "libphonenumber-js": "^1.12.9", "mime-types": "^2.1.35", "multer": "^1.4.5-lts.1", "node-cache": "^5.1.2", "node-cron": "^3.0.2", "node-fetch": "^2.7.0", "node-schedule": "^2.1.1", "pino": "^8.11.0", "protobufjs": "^7.5.3", "qrcode": "^1.5.3", "qrcode-terminal": "^0.12.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "serve-static": "^2.2.0", "sharp": "^0.34.1", "sql.js": "^1.13.0", "uuid": "^9.0.0", "yauzl": "^3.2.0"}, "devDependencies": {"@tailwindcss/forms": "^0.5.3", "@tailwindcss/typography": "^0.5.9", "@types/node": "^18.15.3", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "autoprefixer": "^10.4.14", "commander": "^9.4.1", "concurrently": "^7.6.0", "electron": "^23.1.3", "electron-builder": "^23.6.0", "electron-rebuild": "^3.2.9", "postcss": "^8.4.21", "react-scripts": "5.0.1", "tailwindcss": "^3.2.7", "to-ico": "^1.1.5", "typescript": "^4.9.5", "wait-on": "^7.0.1"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "build": {"appId": "com.leadwave.whatsapp-desktop", "productName": "LeadWave WhatsApp Desktop", "copyright": "Copyright © 2025 LeadWave Team", "buildDependenciesFromSource": false, "nodeGypRebuild": false, "npmRebuild": false, "asar": true, "asarUnpack": ["node_modules/better-sqlite3/**/*", "node_modules/sharp/**/*", "node_modules/@itsukichan/baileys/**/*"], "directories": {"output": "dist", "buildResources": "build-resources"}, "compression": "maximum", "removePackageScripts": true, "files": ["build/**/*", "node_modules/**/*", "package.json", "!node_modules/.cache", "!**/*.{iml,o,hprof,orig,pyc,pyo,rbc,swp,csproj,sln,xproj}", "!.editorconfig", "!**/._*", "!**/{.DS_Store,.git,.hg,.svn,CVS,RCS,SCCS,.gitignore,.gitattributes}", "!**/{__pycache__,thumbs.db,.flowconfig,.idea,.vs,.nyc_output}", "!**/{appveyor.yml,.travis.yml,circle.yml}", "!**/{npm-debug.log,yarn.lock,.yarn-integrity,.yarn-metadata.json}", "!**/*.db", "!**/leadwave*.db", "!data/**/*", "!auth_sessions/**/*", "!src/**/*"], "mac": {"category": "public.app-category.productivity", "icon": "build-resources/assets/app-icon.icns", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "build-resources/entitlements.mac.plist", "entitlementsInherit": "build-resources/entitlements.mac.plist"}, "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "build-resources/assets/app-icon.ico", "publisherName": "LeadWave Team", "verifyUpdateCodeSignature": false, "requestedExecutionLevel": "asInvoker", "artifactName": "${productName}-Setup-${version}.${ext}"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "LeadWave WhatsApp Desktop", "runAfterFinish": true, "perMachine": false}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}, {"target": "rpm", "arch": ["x64"]}], "category": "Office"}, "msi": {"createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "LeadWave WhatsApp Desktop", "runAfterFinish": true, "perMachine": false}, "dmg": {"contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}], "title": "LeadWave WhatsApp Desktop ${version}", "window": {"width": 540, "height": 380}}, "portable": {"artifactName": "LeadWave-WhatsApp-Desktop-${version}-portable.${ext}"}}}